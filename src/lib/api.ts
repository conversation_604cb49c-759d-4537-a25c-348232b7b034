import { env } from "~/env.js";

const API_BASE_URL = env.NEXT_PUBLIC_API_BASE_URL;

export interface ApiResponse<T = any> {
  status: number;
  message: string;
  data?: T;
}

export interface LoginRequest {
  country_code: string;
  phone: string;
  confirmation_code: string;
  device_id: string;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface UserProfile {
  id: number;
  name: string;
  family: string;
  country_code: string;
  country_name: string;
  phone: string;
  email: string;
  birth_date: string;
  gender: "MALE" | "FEMALE";
  userType: "GENERAL" | "MASTER";
  verified_by: "PHONE" | "EMAIL";
  status: "PENDING" | "AWAITING_CONFIRMATION" | "ACTIVE" | "DISABLED";
  created_at: string;
}

class ApiClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    const response = await fetch(url, {
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  async login(data: LoginRequest): Promise<LoginResponse> {
    return this.request<LoginResponse>("/auth/login", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async refreshToken(data: RefreshTokenRequest): Promise<LoginResponse> {
    return this.request<LoginResponse>("/auth/refresh-token", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async getProfile(token: string): Promise<UserProfile> {
    return this.request<UserProfile>("/users/profile", {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  }

  async logout(token: string): Promise<void> {
    return this.request<void>("/users/logout", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  }
}

export const apiClient = new ApiClient(API_BASE_URL);
